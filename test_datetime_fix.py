#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试日期时间字段修复的脚本
"""

import os
import pymysql
from openpyxl import load_workbook
import sys

# 数据库连接信息
DB_CONFIG = {
    'host': '************',
    'user': 'lxt',
    'password': 'Lxt0307+',
    'database': 'qq_day_sale'
}

# 目标表名
TARGET_TABLE = 'wdt_saledetail'

# Excel文件的列名（92列）
EXCEL_COLUMNS = [
    '订单编号', '原始单号', '子单原始单号', '原始子订单号', '订单类型', '支付账号',
    '出库单编号', '仓库', '仓库类型', '店铺', '出库单状态', '出库状态', '分拣序号',
    '商家编码', '货品编号', '货品名称', '货品简称', '品牌', '分类', '规格码',
    '规格名称', '平台货品名称', '平台规格名称', '平台货品ID', '平台规格ID', '条形码',
    '货品数量', '货品原单价', '货品原总金额', '订单总优惠', '邮费', '货品成交价',
    '货品成交总价', '货品总优惠', '货到付款金额', '货品成本', '货品总成本', '固定成本',
    '固定总成本', '订单支付金额', '应收金额', '退款前支付金额', '单品支付金额', '分摊邮费',
    '预估邮资', '邮资成本', '订单包装成本', '订单毛利', '毛利率', '订单固定毛利',
    '固定毛利率', '客户网名', '收件人', '证件号码', '收货地区', '收货地址',
    '收件人手机', '收件人电话', '物流公司', '实际重量', '预估重量', '需开发票',
    '制单人', '打单员', '拣货员', '打包员', '检视员', '业务员', '验货员',
    '打印波次', '物流单打印状态', '发货单打印状态', '分拣单打印状态', '物流单号',
    '分拣单编号', '外部单号', '付款时间', '发货时间', '赠品方式', '买家留言',
    '客服备注', '打印备注', '备注', '包装', '来源组合装编码', '拆自组合装',
    '来源组合装数量', '体积', '分销商', '分销商编号', '下单时间', '审核时间'
]

# 数据库表的列名（93列，不包括自增主键id）
DB_COLUMNS = [
    '订单编号', '原始单号', '子单原始单号', '原始子订单号', '订单类型', '支付账号',
    '出库单编号', '仓库', '仓库类型', '店铺', '出库单状态', '出库状态', '分拣序号',
    '商家编码', '货品编号', '货品名称', '货品简称', '品牌', '分类', '规格码',
    '规格名称', '平台货品名称', '平台规格名称', '平台货品ID', '平台规格ID', '条形码',
    '货品数量', '货品原单价', '货品原总金额', '订单总优惠', '邮费', '货品成交价',
    '货品成交总价', '货品总优惠', '货到付款金额', '货品成本', '货品总成本', '固定成本',
    '固定总成本', '订单支付金额', '应收金额', '退款前支付金额', '单品支付金额', '分摊邮费',
    '预估邮资', '邮资成本', '订单包装成本', '订单毛利', '毛利率', '订单固定毛利',
    '固定毛利率', '客户网名', '收件人', '证件号码', '收货地区', '收货地址',
    '收件人手机', '收件人电话', '物流公司', '实际重量', '预估重量', '需开发票',
    '制单人', '打单员', '拣货员', '打包员', '检视员', '业务员', '验货员',
    '打印波次', '物流单打印状态', '发货单打印状态', '分拣单打印状态', '物流单号',
    '分拣单编号', '外部单号', '付款时间', '发货时间', '下单时间', '审核时间',
    '赠品方式', '买家留言', '客服备注', '打印备注', '备注', '包装',
    '来源组合装编码', '拆自组合装', '来源组合装数量', '体积', '分销商', '分销商编号',
    '分销原始单号'
]

def convert_excel_row_to_db_row(excel_row):
    """将Excel行数据转换为数据库行数据"""
    # 确保Excel行有92列
    excel_data = list(excel_row)
    while len(excel_data) < 92:
        excel_data.append(None)
    
    # 创建数据库行数据（93列）
    db_row = []
    
    # 定义需要特殊处理的日期时间字段
    datetime_fields = {'付款时间', '发货时间', '下单时间', '审核时间'}
    
    # 按照数据库列的顺序填充数据
    for db_col in DB_COLUMNS:
        if db_col in EXCEL_COLUMNS:
            # 找到Excel中对应列的索引
            excel_index = EXCEL_COLUMNS.index(db_col)
            value = excel_data[excel_index]
            
            # 对日期时间字段进行特殊处理
            if db_col in datetime_fields:
                # 如果是空字符串，转换为None（数据库NULL）
                if value == '' or value is None:
                    value = None
                # 如果是字符串但不是空字符串，保持原值（让数据库自己解析）
                # 如果是datetime对象，保持原值
            
            db_row.append(value)
        else:
            # 缺失的列填充None（对应数据库的NULL）
            db_row.append(None)
    
    return tuple(db_row)

def test_single_file(file_path):
    """测试单个文件的前几行数据"""
    try:
        print(f"测试文件: {os.path.basename(file_path)}")
        
        # 创建数据库连接
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        # 加载Excel文件
        wb = load_workbook(file_path)
        ws = wb.active
        
        # 只处理前3行数据进行测试
        test_rows = []
        row_count = 0
        for row in ws.iter_rows(min_row=2, values_only=True):
            if row and any(cell is not None for cell in row):
                db_row = convert_excel_row_to_db_row(row)
                test_rows.append(db_row)
                row_count += 1
                if row_count >= 3:  # 只测试前3行
                    break
        
        if not test_rows:
            print("没有找到有效的数据行")
            return False
        
        # 构建INSERT语句
        column_names = ', '.join([f'`{col}`' for col in DB_COLUMNS])
        placeholders = ', '.join(['%s'] * len(DB_COLUMNS))
        sql = f"INSERT INTO {TARGET_TABLE} ({column_names}) VALUES ({placeholders})"
        
        # 测试插入
        success_count = 0
        for i, row in enumerate(test_rows):
            try:
                cursor.execute(sql, row)
                print(f"✅ 第 {i+1} 行测试成功")
                success_count += 1
                
                # 检查日期时间字段的值
                datetime_values = {}
                for j, col in enumerate(DB_COLUMNS):
                    if col in {'付款时间', '发货时间', '下单时间', '审核时间'}:
                        datetime_values[col] = row[j]
                
                print(f"   日期时间字段: {datetime_values}")
                
            except Exception as e:
                print(f"❌ 第 {i+1} 行测试失败: {str(e)}")
                print(f"   数据: {row[:5]}...")  # 只显示前5个字段
        
        # 回滚事务（不实际插入数据）
        connection.rollback()
        cursor.close()
        connection.close()
        
        print(f"测试完成: {success_count}/{len(test_rows)} 行成功")
        return success_count == len(test_rows)
        
    except Exception as e:
        print(f"测试文件时出错: {str(e)}")
        return False

def main():
    """主函数"""
    # 选择一个测试文件
    test_file = "data/20250220销售出库明细账.xlsx"
    
    if not os.path.exists(test_file):
        print(f"测试文件不存在: {test_file}")
        # 尝试使用第一个可用的文件
        data_files = [f for f in os.listdir('data') if f.endswith('.xlsx')]
        if data_files:
            test_file = os.path.join('data', data_files[0])
            print(f"使用文件: {test_file}")
        else:
            print("没有找到可用的测试文件")
            return
    
    success = test_single_file(test_file)
    
    if success:
        print("\n🎉 测试通过！日期时间字段修复成功")
        print("现在可以安全地运行 importer_fixed.py 进行批量导入")
    else:
        print("\n⚠️ 测试失败，需要进一步检查问题")

if __name__ == "__main__":
    main()
