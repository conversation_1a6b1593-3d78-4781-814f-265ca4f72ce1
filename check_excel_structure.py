#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查Excel文件结构的脚本
用于分析Excel文件的列数、列名和数据格式
"""

import os
from openpyxl import load_workbook

def check_excel_structure(file_path):
    """检查Excel文件的结构"""
    print(f"正在检查文件: {file_path}")
    print("=" * 80)
    
    try:
        # 加载Excel文件
        wb = load_workbook(file_path)
        ws = wb.active
        
        # 获取表头（第一行）
        headers = []
        for cell in ws[1]:
            headers.append(cell.value)
        
        print(f"总列数: {len(headers)}")
        print(f"工作表名称: {ws.title}")
        print(f"总行数: {ws.max_row}")
        print(f"总列数: {ws.max_column}")
        
        print("\n列名列表:")
        for i, header in enumerate(headers, 1):
            print(f"{i:2d}. {header}")
        
        # 检查前几行数据
        print("\n前3行数据示例:")
        for row_num in range(1, min(4, ws.max_row + 1)):
            row_data = []
            for cell in ws[row_num]:
                row_data.append(cell.value)
            print(f"第{row_num}行 ({len(row_data)}列): {row_data[:5]}...")  # 只显示前5列
        
        # 检查是否有空列
        empty_columns = []
        for col_idx, header in enumerate(headers, 1):
            if header is None or str(header).strip() == "":
                empty_columns.append(col_idx)
        
        if empty_columns:
            print(f"\n发现空列: {empty_columns}")
        
        return len(headers), headers
        
    except Exception as e:
        print(f"检查文件时出错: {str(e)}")
        return None, None

def main():
    """主函数"""
    # 检查目标Excel文件
    target_file = "data/20241129-1201销售出库明细账.xlsx"
    
    if not os.path.exists(target_file):
        print(f"文件不存在: {target_file}")
        return
    
    col_count, headers = check_excel_structure(target_file)
    
    if col_count:
        print(f"\n数据库表 wdt_saledetail 的列数:")
        # 从SQL文件计算数据库表的列数（除了自增主键id）
        db_columns = 95  # 根据SQL文件，总共96列，除了自增主键id，实际需要插入95列
        
        print(f"Excel文件列数: {col_count}")
        print(f"数据库表列数: {db_columns} (不包括自增主键id)")
        
        if col_count != db_columns:
            print(f"\n⚠️  列数不匹配!")
            print(f"差异: {col_count - db_columns}")
            if col_count > db_columns:
                print("Excel文件列数多于数据库表列数")
            else:
                print("Excel文件列数少于数据库表列数")
        else:
            print("\n✅ 列数匹配!")

if __name__ == "__main__":
    main()
