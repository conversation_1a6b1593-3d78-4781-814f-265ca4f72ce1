# 数据库插入错误解决方案

## 问题描述

在处理Excel文件 `data\20241129-1201销售出库明细账.xlsx` 时，出现数据库插入错误：

```
错误代码：1136
错误描述：Column count doesn't match value count at row 1（第1行的列数与值数不匹配）
```

## 问题分析

### 根本原因：列数不匹配

通过详细分析发现：

1. **Excel文件列数**：92列
2. **数据库表列数**：93列（不包括自增主键`id`）
3. **缺失的列**：`分销原始单号`

### 原始代码问题

原始的 `importer.py` 使用了以下SQL语句：

```python
sql = f"INSERT INTO {TARGET_TABLE} VALUES ({placeholders})"
```

这种 `INSERT INTO table VALUES (...)` 语句要求提供所有列的值，但Excel文件缺少一列，导致列数不匹配。

## 解决方案

### 1. 修复策略

创建了新的 `importer_fixed.py` 文件，采用以下策略：

1. **明确列映射**：定义Excel列和数据库列的对应关系
2. **数据转换**：将Excel行数据转换为数据库行数据
3. **指定列名插入**：使用 `INSERT INTO table (columns) VALUES (...)` 语句
4. **处理缺失列**：为缺失的列填充 `NULL` 值

### 2. 关键修改

#### 列定义
```python
# Excel文件的列名（92列）
EXCEL_COLUMNS = [
    '订单编号', '原始单号', '子单原始单号', '原始子订单号', '订单类型', '支付账号',
    # ... 其他列
    '下单时间', '审核时间'  # 注意：缺少'分销原始单号'
]

# 数据库表的列名（93列，不包括自增主键id）
DB_COLUMNS = [
    '订单编号', '原始单号', '子单原始单号', '原始子订单号', '订单类型', '支付账号',
    # ... 其他列
    '分销原始单号'  # 这是Excel中缺失的列
]
```

#### 数据转换函数
```python
def convert_excel_row_to_db_row(excel_row):
    """将Excel行数据转换为数据库行数据"""
    # 确保Excel行有92列
    excel_data = list(excel_row)
    while len(excel_data) < 92:
        excel_data.append(None)
    
    # 创建数据库行数据（93列）
    db_row = []
    
    # 按照数据库列的顺序填充数据
    for db_col in DB_COLUMNS:
        if db_col in EXCEL_COLUMNS:
            # 找到Excel中对应列的索引
            excel_index = EXCEL_COLUMNS.index(db_col)
            db_row.append(excel_data[excel_index])
        else:
            # 缺失的列填充None（对应数据库的NULL）
            db_row.append(None)
    
    return tuple(db_row)
```

#### 改进的插入语句
```python
def insert_batch_data(cursor, batch_data):
    """批量插入数据"""
    if not batch_data:
        return

    # 构建指定列名的INSERT语句
    column_names = ', '.join([f'`{col}`' for col in DB_COLUMNS])
    placeholders = ', '.join(['%s'] * len(DB_COLUMNS))
    sql = f"INSERT INTO {TARGET_TABLE} ({column_names}) VALUES ({placeholders})"

    try:
        cursor.executemany(sql, batch_data)
        print(f"成功批量插入 {len(batch_data)} 条数据")
    except Exception as e:
        print(f"批量插入数据时出错: {str(e)}")
        # 如果批量插入失败，尝试逐行插入
        for i, row in enumerate(batch_data):
            try:
                cursor.execute(sql, row)
                if (i + 1) % 100 == 0:
                    print(f"已逐行插入 {i + 1} 条数据")
            except Exception as row_error:
                print(f"插入行数据失败: {row} - {str(row_error)}")
```

## 测试结果

### 修复前
```
错误代码：1136
错误描述：Column count doesn't match value count at row 1
```

### 修复后
```
找到 1 个Excel文件（限制处理1个文件进行测试）
将要处理的文件: 20241129-1201销售出库明细账.xlsx
开始处理文件: data\20241129-1201销售出库明细账.xlsx
成功批量插入 1000 条数据
成功批量插入 212 条数据
完成处理文件: data\20241129-1201销售出库明细账.xlsx

==================================================
处理结果汇总:
  成功处理: 20241129-1201销售出库明细账.xlsx

总共处理 1 个文件
总耗时: 0.85 秒
==================================================
```

### 数据验证
```
数据库表 wdt_saledetail 总记录数: 1212

最近插入的5条记录:
订单编号                 货品名称                           数量       单价         总价         分销原始单号
JY202412010926       原味                             3        1.39       4.18       NULL
JY202412010926       清香牛肉                           2        2.09       4.18       NULL
JY202412010926       气泡袋                            1        4.18       4.18       NULL

订单号包含 'JY202411300932' 的记录:
订单号: JY202411300932
商品: 原味【杯面】
数量: 8, 单价: 3.73, 总价: 29.81

分销原始单号列统计:
总记录数: 1212
非空记录数: 0
空值记录数: 1212
```

## 使用说明

### 1. 使用修复后的导入器
```bash
python importer_fixed.py
```

### 2. 验证数据
```bash
python verify_data.py
```

### 3. 检查Excel结构（可选）
```bash
python check_excel_structure.py
python compare_columns.py
```

## 总结

1. **问题根源**：Excel文件缺少"分销原始单号"列，导致列数不匹配
2. **解决方案**：使用列名映射和数据转换，为缺失列填充NULL值
3. **结果**：成功插入1212条记录，所有数据正确导入
4. **改进**：代码更加健壮，能够处理列数不匹配的情况

## 建议

1. **后续处理**：使用 `importer_fixed.py` 替代原始的 `importer.py`
2. **批量处理**：可以修改 `MAX_WORKERS` 和文件限制来处理更多文件
3. **监控**：定期检查数据完整性和导入结果
4. **备份**：在大批量导入前建议备份数据库
