#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
比较Excel文件和数据库表的列结构
找出缺失的列
"""

import os
from openpyxl import load_workbook

def get_excel_columns(file_path):
    """获取Excel文件的列名"""
    wb = load_workbook(file_path)
    ws = wb.active
    
    headers = []
    for cell in ws[1]:
        headers.append(cell.value)
    
    return headers

def get_db_columns():
    """获取数据库表的列名（从SQL文件中提取，排除自增主键id）"""
    db_columns = [
        '订单编号', '原始单号', '子单原始单号', '原始子订单号', '订单类型', '支付账号',
        '出库单编号', '仓库', '仓库类型', '店铺', '出库单状态', '出库状态', '分拣序号',
        '商家编码', '货品编号', '货品名称', '货品简称', '品牌', '分类', '规格码',
        '规格名称', '平台货品名称', '平台规格名称', '平台货品ID', '平台规格ID', '条形码',
        '货品数量', '货品原单价', '货品原总金额', '订单总优惠', '邮费', '货品成交价',
        '货品成交总价', '货品总优惠', '货到付款金额', '货品成本', '货品总成本', '固定成本',
        '固定总成本', '订单支付金额', '应收金额', '退款前支付金额', '单品支付金额', '分摊邮费',
        '预估邮资', '邮资成本', '订单包装成本', '订单毛利', '毛利率', '订单固定毛利',
        '固定毛利率', '客户网名', '收件人', '证件号码', '收货地区', '收货地址',
        '收件人手机', '收件人电话', '物流公司', '实际重量', '预估重量', '需开发票',
        '制单人', '打单员', '拣货员', '打包员', '检视员', '业务员', '验货员',
        '打印波次', '物流单打印状态', '发货单打印状态', '分拣单打印状态', '物流单号',
        '分拣单编号', '外部单号', '付款时间', '发货时间', '下单时间', '审核时间',
        '赠品方式', '买家留言', '客服备注', '打印备注', '备注', '包装',
        '来源组合装编码', '拆自组合装', '来源组合装数量', '体积', '分销商', '分销商编号',
        '分销原始单号'
    ]
    return db_columns

def main():
    """主函数"""
    target_file = "data/20241129-1201销售出库明细账.xlsx"
    
    if not os.path.exists(target_file):
        print(f"文件不存在: {target_file}")
        return
    
    # 获取Excel和数据库的列名
    excel_columns = get_excel_columns(target_file)
    db_columns = get_db_columns()
    
    print("列结构比较分析")
    print("=" * 80)
    print(f"Excel文件列数: {len(excel_columns)}")
    print(f"数据库表列数: {len(db_columns)}")
    print(f"差异: {len(excel_columns) - len(db_columns)}")
    
    # 找出Excel中有但数据库中没有的列
    excel_only = []
    for col in excel_columns:
        if col not in db_columns:
            excel_only.append(col)
    
    # 找出数据库中有但Excel中没有的列
    db_only = []
    for col in db_columns:
        if col not in excel_columns:
            db_only.append(col)
    
    if excel_only:
        print(f"\nExcel中有但数据库中没有的列 ({len(excel_only)}个):")
        for i, col in enumerate(excel_only, 1):
            print(f"  {i}. {col}")
    
    if db_only:
        print(f"\n数据库中有但Excel中没有的列 ({len(db_only)}个):")
        for i, col in enumerate(db_only, 1):
            print(f"  {i}. {col}")
    
    # 检查列的顺序
    print(f"\n列顺序比较 (前20列):")
    print(f"{'序号':<4} {'Excel列名':<20} {'数据库列名':<20} {'匹配'}")
    print("-" * 70)
    
    for i in range(min(20, len(excel_columns), len(db_columns))):
        excel_col = excel_columns[i] if i < len(excel_columns) else "N/A"
        db_col = db_columns[i] if i < len(db_columns) else "N/A"
        match = "✅" if excel_col == db_col else "❌"
        print(f"{i+1:<4} {excel_col:<20} {db_col:<20} {match}")

if __name__ == "__main__":
    main()
