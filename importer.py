import os
import pymysql
from openpyxl import load_workbook
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

# 数据库连接信息
DB_CONFIG = {
    'host': '************',
    'user': 'lxt',
    'password': 'Lxt0307+',
    'database': 'qq_day_sale'
}

# 目标表名
TARGET_TABLE = 'wdt_saledetail'

# 数据目录
DATA_FOLDER = 'data'

# 线程池大小（测试时只处理1个文件，使用1个线程）
MAX_WORKERS = 1

def get_db_connection():
    """创建数据库连接"""
    return pymysql.connect(**DB_CONFIG)

def process_excel_file(file_path):
    """处理单个Excel文件"""
    try:
        print(f"开始处理文件: {file_path}")

        # 创建数据库连接
        connection = get_db_connection()
        cursor = connection.cursor()

        # 加载Excel文件
        wb = load_workbook(file_path)
        ws = wb.active

        # 批量插入数据
        batch_data = []
        batch_size = 1000  # 每批次插入1000条数据

        for row in ws.iter_rows(min_row=2, values_only=True):
            if row and any(cell is not None for cell in row):  # 跳过空行
                batch_data.append(row)

                if len(batch_data) >= batch_size:
                    insert_batch_data(cursor, batch_data)
                    batch_data = []

        # 插入剩余数据
        if batch_data:
            insert_batch_data(cursor, batch_data)

        # 提交事务
        connection.commit()
        cursor.close()
        connection.close()

        print(f"完成处理文件: {file_path}")
        return f"成功处理: {os.path.basename(file_path)}"

    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {str(e)}")
        return f"处理失败: {os.path.basename(file_path)} - {str(e)}"

def insert_batch_data(cursor, batch_data):
    """批量插入数据"""
    if not batch_data:
        return

    # 获取第一行数据的列数来构建占位符
    placeholders = ', '.join(['%s'] * len(batch_data[0]))
    sql = f"INSERT INTO {TARGET_TABLE} VALUES ({placeholders})"

    try:
        cursor.executemany(sql, batch_data)
    except Exception as e:
        print(f"批量插入数据时出错: {str(e)}")
        # 如果批量插入失败，尝试逐行插入
        for row in batch_data:
            try:
                cursor.execute(sql, row)
            except Exception as row_error:
                print(f"插入行数据失败: {row} - {str(row_error)}")

def main():
    """主函数"""
    start_time = time.time()

    # 检查数据目录是否存在
    if not os.path.exists(DATA_FOLDER):
        print(f"错误: 数据目录 '{DATA_FOLDER}' 不存在")
        return

    # 获取所有Excel文件
    excel_files = [f for f in os.listdir(DATA_FOLDER)
                   if f.endswith('.xlsx') or f.endswith('.xls')]

    if not excel_files:
        print(f"在目录 '{DATA_FOLDER}' 中没有找到Excel文件")
        return

    # 限制只处理第一个文件进行测试
    excel_files = excel_files[:1]

    print(f"找到 {len(excel_files)} 个Excel文件（限制处理1个文件进行测试）")
    print(f"将要处理的文件: {excel_files[0]}")

    # 构建完整文件路径
    file_paths = [os.path.join(DATA_FOLDER, file) for file in excel_files]

    # 使用线程池并行处理文件
    with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        # 提交所有任务
        future_to_file = {executor.submit(process_excel_file, file_path): file_path
                         for file_path in file_paths}

        # 收集结果
        results = []
        for future in as_completed(future_to_file):
            file_path = future_to_file[future]
            try:
                result = future.result()
                results.append(result)
            except Exception as exc:
                error_msg = f"文件 {file_path} 处理异常: {exc}"
                print(error_msg)
                results.append(error_msg)

    end_time = time.time()

    # 打印处理结果
    print("\n" + "="*50)
    print("处理结果汇总:")
    for result in results:
        print(f"  {result}")

    print(f"\n总共处理 {len(excel_files)} 个文件")
    print(f"总耗时: {end_time - start_time:.2f} 秒")
    print("="*50)

if __name__ == "__main__":
    main()