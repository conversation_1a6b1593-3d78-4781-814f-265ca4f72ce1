#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证数据插入结果的脚本
"""

import pymysql

# 数据库连接信息
DB_CONFIG = {
    'host': '************',
    'user': 'lxt',
    'password': 'Lxt0307+',
    'database': 'qq_day_sale'
}

TARGET_TABLE = 'wdt_saledetail'

def verify_data():
    """验证数据插入结果"""
    try:
        # 创建数据库连接
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        # 查询总记录数
        cursor.execute(f"SELECT COUNT(*) FROM {TARGET_TABLE}")
        total_count = cursor.fetchone()[0]
        print(f"数据库表 {TARGET_TABLE} 总记录数: {total_count}")
        
        # 查询最近插入的几条记录
        cursor.execute(f"""
            SELECT 订单编号, 货品名称, 货品数量, 货品成交价, 货品成交总价, 分销原始单号
            FROM {TARGET_TABLE}
            LIMIT 5
        """)
        
        recent_records = cursor.fetchall()
        print(f"\n最近插入的5条记录:")
        print("-" * 100)
        print(f"{'订单编号':<20} {'货品名称':<30} {'数量':<8} {'单价':<10} {'总价':<10} {'分销原始单号':<15}")
        print("-" * 100)
        
        for record in recent_records:
            order_no = record[0] or "N/A"
            product_name = record[1] or "N/A"
            quantity = record[2] or 0
            unit_price = record[3] or 0
            total_price = record[4] or 0
            distribution_no = record[5] or "NULL"
            
            # 截断过长的商品名称
            if len(product_name) > 28:
                product_name = product_name[:25] + "..."
            
            print(f"{order_no:<20} {product_name:<30} {quantity:<8} {unit_price:<10} {total_price:<10} {distribution_no:<15}")
        
        # 查询特定订单号的记录（您提到的JY202411300932）
        cursor.execute(f"""
            SELECT 订单编号, 货品名称, 货品数量, 货品成交价, 货品成交总价
            FROM {TARGET_TABLE} 
            WHERE 订单编号 LIKE '%JY202411300932%'
            LIMIT 3
        """)
        
        specific_records = cursor.fetchall()
        if specific_records:
            print(f"\n订单号包含 'JY202411300932' 的记录:")
            print("-" * 80)
            for record in specific_records:
                print(f"订单号: {record[0]}")
                print(f"商品: {record[1]}")
                print(f"数量: {record[2]}, 单价: {record[3]}, 总价: {record[4]}")
                print("-" * 40)
        else:
            print(f"\n未找到订单号包含 'JY202411300932' 的记录")
        
        # 检查分销原始单号列的情况
        cursor.execute(f"""
            SELECT 
                COUNT(*) as total,
                COUNT(分销原始单号) as not_null_count,
                COUNT(*) - COUNT(分销原始单号) as null_count
            FROM {TARGET_TABLE}
        """)
        
        distribution_stats = cursor.fetchone()
        print(f"\n分销原始单号列统计:")
        print(f"总记录数: {distribution_stats[0]}")
        print(f"非空记录数: {distribution_stats[1]}")
        print(f"空值记录数: {distribution_stats[2]}")
        
        cursor.close()
        connection.close()
        
        print(f"\n✅ 数据验证完成！")
        
    except Exception as e:
        print(f"验证数据时出错: {str(e)}")

if __name__ == "__main__":
    verify_data()
